#include <iostream>
#include <string>
#include <cstring>
#include <map>
#include <algorithm>
#include <sstream>
#include <fstream>
#if defined(_WIN32)
#include <windows.h>
#endif
#include <GLFW/glfw3.h>
#include <GL/gl.h>
#include <imgui.h>
#include <imgui_impl_glfw.h>
#include <imgui_impl_opengl3.h>
#include "imfilebrowser.h"
#include "piano_keyboard.h"
#include "opengl_renderer.h"
#include "audio_engine.h"
#include "embedded_fonts.h"
#include "config_manager.h"
#include "note_indicator.h"

// Global variables for mouse input
double g_mouse_x = 0.0;
double g_mouse_y = 0.0;
bool g_mouse_is_down = false;

// Global variables for window management
PianoKeyboard* g_piano = nullptr;
OpenGLRenderer* g_renderer = nullptr;
AudioEngine* g_audio_engine = nullptr;

// Global variables for audio
AudioEngine g_audio;
bool g_audio_initialized = false;

// Global note indicator
NoteIndicator g_note_indicator;

// Global configuration manager
ConfigManager g_config;

// Global file browser for soundfont selection
ImGui::FileBrowser g_soundfont_browser(ImGuiFileBrowserFlags_CloseOnEsc);

// Global file browser for background image selection
ImGui::FileBrowser g_image_browser(ImGuiFileBrowserFlags_CloseOnEsc);

// Global font pointers
ImFont* g_font_regular = nullptr;
ImFont* g_font_large = nullptr;

// Global variables for PC keyboard input
static std::map<int, int> g_key_to_note_map; // Maps GLFW key codes to MIDI note numbers
static std::map<int, bool> g_key_pressed_state; // Tracks which keys are currently pressed

// Error callback for GLFW
void glfw_error_callback(int error, const char* description) {
    std::cerr << "GLFW Error " << error << ": " << description << std::endl;
}

// Mouse callback for GLFW
void mouse_button_callback(GLFWwindow* window, int button, int action, int mods) {
    // Let ImGui handle the event first
    ImGuiIO& io = ImGui::GetIO();

    if (button == GLFW_MOUSE_BUTTON_LEFT) {
        if (action == GLFW_PRESS) {
            // Only set mouse down if ImGui is not using the mouse
            g_mouse_is_down = !io.WantCaptureMouse;
        } else if (action == GLFW_RELEASE) {
            g_mouse_is_down = false;
        }
    }
}

void cursor_position_callback(GLFWwindow* window, double xpos, double ypos) {
    g_mouse_x = xpos;
    g_mouse_y = ypos;
}

void window_size_callback(GLFWwindow* window, int width, int height) {
    if (g_piano && g_renderer) {
        g_renderer->SetViewport(width, height);
        g_piano->UpdateLayout(width, height);

        // Save window size to config
        auto& config = g_config.GetConfig();
        config.window.width = width;
        config.window.height = height;
        g_config.AutoSave();
    }
}

// Keyboard callback for PC keyboard input
void key_callback(GLFWwindow* window, int key, int scancode, int action, int mods) {
    // Handle special key combinations for panic functions
    if (action == GLFW_PRESS) {
        // Ctrl+Shift+P = Panic Restart
        if (key == GLFW_KEY_P && (mods & GLFW_MOD_CONTROL) && (mods & GLFW_MOD_SHIFT)) {
            std::cout << "Panic restart triggered by keyboard shortcut (Ctrl+Shift+P)" << std::endl;
            if (g_audio_engine) {
                g_audio_engine->PanicRestart();
            }
            return;
        }

        // Ctrl+Shift+S = Emergency Stop All Notes
        if (key == GLFW_KEY_S && (mods & GLFW_MOD_CONTROL) && (mods & GLFW_MOD_SHIFT)) {
            std::cout << "Emergency stop triggered by keyboard shortcut (Ctrl+Shift+S)" << std::endl;
            if (g_audio_engine) {
                g_audio_engine->EmergencyStopAllNotes();
            }
            return;
        }

        // Ctrl+Shift+T = Test Audio
        if (key == GLFW_KEY_T && (mods & GLFW_MOD_CONTROL) && (mods & GLFW_MOD_SHIFT)) {
            std::cout << "Test audio triggered by keyboard shortcut (Ctrl+Shift+T)" << std::endl;
            if (g_audio_engine) {
                g_audio_engine->PlayTestTone();
            }
            return;
        }
    }

    // Check if ImGui wants to capture keyboard input
    ImGuiIO& io = ImGui::GetIO();
    if (io.WantCaptureKeyboard) {
        return; // Let ImGui handle the keyboard input
    }

    // Check if this key is mapped to a note
    auto it = g_key_to_note_map.find(key);
    if (it != g_key_to_note_map.end()) {
        int base_note = it->second;

        // Apply PC keyboard transpose and octave shift from cheat tool settings
        auto& config = g_config.GetConfig();
        int transpose_offset = config.cheat_tool.pc_keyboard_transpose + (config.cheat_tool.pc_keyboard_octave * 12);

        // Add 1 octave (12 semitones) if Shift key is pressed
        if (mods & GLFW_MOD_SHIFT) {
            transpose_offset += 12;
        }

        int final_note = base_note + transpose_offset;

        // Clamp to valid MIDI range (0-127)
        final_note = std::max(0, std::min(127, final_note));

        if (action == GLFW_PRESS) {
            // Key pressed - only trigger if not already pressed (to avoid repeats)
            if (g_key_pressed_state[key] == false) {
                g_key_pressed_state[key] = true;
                if (g_piano) {
                    g_piano->SetKeyPressedWithVelocity(final_note, true, g_mouse_y);
                }
            }
        } else if (action == GLFW_RELEASE) {
            // Key released
            g_key_pressed_state[key] = false;
            if (g_piano) {
                g_piano->SetKeyPressedWithVelocity(final_note, false, g_mouse_y);
            }
        }
    }
}

// Initialize PC keyboard to MIDI note mapping
void initialize_keyboard_mapping() {
    // Clear existing mappings
    g_key_to_note_map.clear();
    g_key_pressed_state.clear();

    // Helper function to create note mapping
    auto map_key = [](int glfw_key, int note) {
        g_key_to_note_map[glfw_key] = note;
        g_key_pressed_state[glfw_key] = false;
    };

    // Lower row - Piano layout starting from A2 (Z = A2/ラ)
    map_key(GLFW_KEY_A, 44);      //
    map_key(GLFW_KEY_Z, 45);      // A2 - ラ
    map_key(GLFW_KEY_S, 46);      // A#2/Bb2 (black key)
    map_key(GLFW_KEY_X, 47);      // B2 - シ
    map_key(GLFW_KEY_C, 48);      // C3 - ド
    map_key(GLFW_KEY_F, 49);      // C#3/Db3 (black key)
    map_key(GLFW_KEY_V, 50);      // D3 - レ
    map_key(GLFW_KEY_G, 51);      // D#3/Eb3 (black key)
    map_key(GLFW_KEY_B, 52);      // E3 - ミ
    map_key(GLFW_KEY_N, 53);      // F3 - ファ
    map_key(GLFW_KEY_J, 54);      // F#3/Gb3 (black key)
    map_key(GLFW_KEY_M, 55);      // G3 - ソ
    map_key(GLFW_KEY_K, 56);      // G#3/Ab3 (black key)
    map_key(GLFW_KEY_COMMA, 57);  // A3 - ラ
    map_key(GLFW_KEY_L, 58);      // A#3/Bb3 (black key)
    map_key(GLFW_KEY_PERIOD, 59); // B3 - シ
    map_key(GLFW_KEY_SLASH, 60); // C4 - ド (Middle C)
    map_key(GLFW_KEY_APOSTROPHE, 61);  // C#4/Db4 (black key)

    // Upper row - Higher octave starting from A3 (Q = A3/ラ)
    map_key(GLFW_KEY_1, 56);      //
    map_key(GLFW_KEY_Q, 57);      // A3 - ラ
    map_key(GLFW_KEY_2, 58);      // A#3/Bb3 (black key)
    map_key(GLFW_KEY_W, 59);      // B3 - シ
    map_key(GLFW_KEY_E, 60);      // C4 - ド (Middle C)
    map_key(GLFW_KEY_4, 61);      // C#4/Db4 (black key)
    map_key(GLFW_KEY_R, 62);      // D4 - レ
    map_key(GLFW_KEY_5, 63);      // D#4/Eb4 (black key)
    map_key(GLFW_KEY_T, 64);      // E4 - ミ
    map_key(GLFW_KEY_Y, 65);      // F4 - ファ
    map_key(GLFW_KEY_7, 66);      // F#4/Gb4 (black key)
    map_key(GLFW_KEY_U, 67);      // G4 - ソ
    map_key(GLFW_KEY_8, 68);      // G#4/Ab4 (black key)
    map_key(GLFW_KEY_I, 69);      // A4 - ラ
    map_key(GLFW_KEY_9, 70);      // A#4/Bb4 (black key)
    map_key(GLFW_KEY_O, 71);      // B4 - シ
    map_key(GLFW_KEY_P, 72);      // C5 - ド
    map_key(GLFW_KEY_MINUS, 73);  // C#5/Db5 (black key)
    map_key(GLFW_KEY_LEFT_BRACKET, 74);  // D5 - レ
    map_key(GLFW_KEY_EQUAL, 75);  // D#5/Eb5 (black key)
    map_key(GLFW_KEY_RIGHT_BRACKET, 76); // E5 - ミ
}

int main(int argc, char** argv) {
    std::cout << "Starting Piano Keyboard Application..." << std::endl;
    std::cout.flush();

    // Initialize configuration manager
    if (!g_config.Initialize()) {
        std::cerr << "Failed to initialize configuration manager" << std::endl;
        return -1;
    }

    // Setup GLFW
    glfwSetErrorCallback(glfw_error_callback);
    if (!glfwInit()) {
        std::cerr << "Failed to initialize GLFW" << std::endl;
        return -1;
    }

    // GL 3.0 + GLSL 130
    const char* glsl_version = "#version 130";
    glfwWindowHint(GLFW_CONTEXT_VERSION_MAJOR, 3);
    glfwWindowHint(GLFW_CONTEXT_VERSION_MINOR, 0);

    // Get window config before creating window
    const auto& window_config = g_config.GetConfig().window;

    // Enable window transparency for aero effects if configured
    if (window_config.transparent_framebuffer) {
        glfwWindowHint(GLFW_TRANSPARENT_FRAMEBUFFER, GLFW_TRUE);
        // Request alpha channel in framebuffer
        glfwWindowHint(GLFW_ALPHA_BITS, 8);
    }

    // Create window with graphics context (using config settings)
    GLFWwindow* window = glfwCreateWindow(window_config.width, window_config.height, "Piano Keyboard", NULL, NULL);
    if (window == NULL) {
        std::cerr << "Failed to create GLFW window" << std::endl;
        glfwTerminate();
        return -1;
    }
    glfwMakeContextCurrent(window);

    // Set vsync based on config
    glfwSwapInterval(window_config.vsync_enabled ? 1 : 0);

    // Set GLFW callbacks
    glfwSetMouseButtonCallback(window, mouse_button_callback);
    glfwSetCursorPosCallback(window, cursor_position_callback);
    glfwSetWindowSizeCallback(window, window_size_callback);
    glfwSetKeyCallback(window, key_callback);

    // Initialize PC keyboard to MIDI note mapping
    initialize_keyboard_mapping();

    // Setup Dear ImGui context
    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    ImGuiIO& io = ImGui::GetIO(); (void)io;
    io.ConfigFlags |= ImGuiConfigFlags_NavEnableKeyboard;

    // Setup Dear ImGui style
    ImGuiStyle& style = ImGui::GetStyle();
    style.Alpha = 1.0;
    style.WindowRounding = 3;
    style.GrabRounding = 1;
    style.GrabMinSize = 20;
    style.FrameRounding = 3;

    style.Colors[ImGuiCol_Text] = ImVec4(0.00f, 1.00f, 1.00f, 1.00f);
    style.Colors[ImGuiCol_TextDisabled] = ImVec4(0.00f, 0.40f, 0.41f, 1.00f);
    style.Colors[ImGuiCol_WindowBg] = ImVec4(0.00f, 0.00f, 0.00f, 0.83f);
    style.Colors[ImGuiCol_ChildBg] = ImVec4(0.00f, 0.00f, 0.00f, 0.00f);
    style.Colors[ImGuiCol_Border] = ImVec4(0.00f, 1.00f, 1.00f, 0.65f);
    style.Colors[ImGuiCol_BorderShadow] = ImVec4(0.00f, 0.00f, 0.00f, 0.00f);
    style.Colors[ImGuiCol_FrameBg] = ImVec4(0.44f, 0.80f, 0.80f, 0.18f);
    style.Colors[ImGuiCol_FrameBgHovered] = ImVec4(0.44f, 0.80f, 0.80f, 0.27f);
    style.Colors[ImGuiCol_FrameBgActive] = ImVec4(0.44f, 0.81f, 0.86f, 0.66f);
    style.Colors[ImGuiCol_TitleBg] = ImVec4(0.14f, 0.18f, 0.21f, 0.73f);
    style.Colors[ImGuiCol_TitleBgCollapsed] = ImVec4(0.00f, 0.00f, 0.00f, 0.54f);
    style.Colors[ImGuiCol_TitleBgActive] = ImVec4(0.00f, 1.00f, 1.00f, 0.27f);
    style.Colors[ImGuiCol_MenuBarBg] = ImVec4(0.00f, 0.00f, 0.00f, 0.20f);
    style.Colors[ImGuiCol_ScrollbarBg] = ImVec4(0.22f, 0.29f, 0.30f, 0.71f);
    style.Colors[ImGuiCol_ScrollbarGrab] = ImVec4(0.00f, 1.00f, 1.00f, 0.44f);
    style.Colors[ImGuiCol_ScrollbarGrabHovered] = ImVec4(0.00f, 1.00f, 1.00f, 0.74f);
    style.Colors[ImGuiCol_ScrollbarGrabActive] = ImVec4(0.00f, 1.00f, 1.00f, 1.00f);
    style.Colors[ImGuiCol_PopupBg] = ImVec4(0.16f, 0.24f, 0.22f, 0.60f);
    style.Colors[ImGuiCol_CheckMark] = ImVec4(0.00f, 1.00f, 1.00f, 0.68f);
    style.Colors[ImGuiCol_SliderGrab] = ImVec4(0.00f, 1.00f, 1.00f, 0.36f);
    style.Colors[ImGuiCol_SliderGrabActive] = ImVec4(0.00f, 1.00f, 1.00f, 0.76f);
    style.Colors[ImGuiCol_Button] = ImVec4(0.00f, 0.65f, 0.65f, 0.46f);
    style.Colors[ImGuiCol_ButtonHovered] = ImVec4(0.01f, 1.00f, 1.00f, 0.43f);
    style.Colors[ImGuiCol_ButtonActive] = ImVec4(0.00f, 1.00f, 1.00f, 0.62f);
    style.Colors[ImGuiCol_Header] = ImVec4(0.00f, 1.00f, 1.00f, 0.33f);
    style.Colors[ImGuiCol_HeaderHovered] = ImVec4(0.00f, 1.00f, 1.00f, 0.42f);
    style.Colors[ImGuiCol_HeaderActive] = ImVec4(0.00f, 1.00f, 1.00f, 0.54f);
    style.Colors[ImGuiCol_Separator] = ImVec4(0.00f, 0.50f, 0.50f, 0.33f);
    style.Colors[ImGuiCol_SeparatorHovered] = ImVec4(0.00f, 0.50f, 0.50f, 0.47f);
    style.Colors[ImGuiCol_SeparatorActive] = ImVec4(0.00f, 0.70f, 0.70f, 1.00f);
    style.Colors[ImGuiCol_ResizeGrip] = ImVec4(0.00f, 1.00f, 1.00f, 0.54f);
    style.Colors[ImGuiCol_ResizeGripHovered] = ImVec4(0.00f, 1.00f, 1.00f, 0.74f);
    style.Colors[ImGuiCol_ResizeGripActive] = ImVec4(0.00f, 1.00f, 1.00f, 1.00f);
    style.Colors[ImGuiCol_Tab] = ImVec4(0.00f, 0.78f, 0.78f, 0.35f);
    style.Colors[ImGuiCol_TabHovered] = ImVec4(0.00f, 0.78f, 0.78f, 0.47f);
    style.Colors[ImGuiCol_TabActive] = ImVec4(0.00f, 0.78f, 0.78f, 1.00f);
    style.Colors[ImGuiCol_PlotLines] = ImVec4(0.00f, 1.00f, 1.00f, 1.00f);
    style.Colors[ImGuiCol_PlotLinesHovered] = ImVec4(0.00f, 1.00f, 1.00f, 1.00f);
    style.Colors[ImGuiCol_PlotHistogram] = ImVec4(0.00f, 1.00f, 1.00f, 1.00f);
    style.Colors[ImGuiCol_PlotHistogramHovered] = ImVec4(0.00f, 1.00f, 1.00f, 1.00f);
    style.Colors[ImGuiCol_TextSelectedBg] = ImVec4(0.00f, 1.00f, 1.00f, 0.22f);
    style.Colors[ImGuiCol_DragDropTarget] = ImVec4(0.00f, 1.00f, 1.00f, 0.90f);
    style.Colors[ImGuiCol_NavHighlight] = ImVec4(0.00f, 1.00f, 1.00f, 1.00f);
    style.Colors[ImGuiCol_NavWindowingHighlight] = ImVec4(1.00f, 1.00f, 1.00f, 0.70f);
    style.Colors[ImGuiCol_NavWindowingDimBg] = ImVec4(0.80f, 0.80f, 0.80f, 0.20f);
    style.Colors[ImGuiCol_ModalWindowDimBg] = ImVec4(0.04f, 0.10f, 0.09f, 0.51f);

    // Load custom fonts
    // Use the existing io reference

    // Add default font first (as fallback)
    io.Fonts->AddFontDefault();

    // Load GothicA1 font from embedded data
    ImFontConfig font_config;
    font_config.FontDataOwnedByAtlas = false; // We own the font data
    strcpy(font_config.Name, "GothicA1 Regular");

    // Add GothicA1 font with different sizes using embedded data
    g_font_regular = io.Fonts->AddFontFromMemoryTTF(
        (void*)GothicA1_Regular_ttf_data,
        GothicA1_Regular_ttf_size,
        16.0f,
        &font_config
    );

    g_font_large = io.Fonts->AddFontFromMemoryTTF(
        (void*)GothicA1_Regular_ttf_data,
        GothicA1_Regular_ttf_size,
        20.0f,
        &font_config
    );

    if (g_font_regular && g_font_large) {
        std::cout << "Successfully loaded GothicA1 font from embedded data" << std::endl;
    } else {
        std::cout << "Warning: Failed to load GothicA1 font from embedded data" << std::endl;
    }

    // Note: Don't call io.Fonts->Build() manually with newer ImGui backends

    // Setup Platform/Renderer backends
    ImGui_ImplGlfw_InitForOpenGL(window, true);
    ImGui_ImplOpenGL3_Init(glsl_version);

    // Initialize OpenGL renderer
    OpenGLRenderer renderer;
    renderer.Initialize(1280, 720);

    // Initialize audio engine
    std::cout << "Initializing Audio Engine..." << std::endl;
    std::cout.flush();
    g_audio_initialized = g_audio.Initialize();
    if (!g_audio_initialized) {
        std::cerr << "Warning: Audio engine failed to initialize. Audio will be disabled." << std::endl;
        std::cerr.flush();
    } else {
        std::cout << "Audio engine initialized successfully!" << std::endl;
        std::cout.flush();
    }

    // Initialize piano keyboard
    PianoKeyboard piano;
    piano.Initialize(&g_audio);

    // Get configuration reference
    auto& config = g_config.GetConfig();

    // Initialize note indicator
    g_note_indicator.Initialize();
    g_note_indicator.SetPosition(Vec2(config.display.note_indicator_position[0], config.display.note_indicator_position[1]));
    g_note_indicator.SetSize(Vec2(config.display.note_indicator_size[0], config.display.note_indicator_size[1]));
    g_note_indicator.SetColor(Color(config.display.note_indicator_color[0], config.display.note_indicator_color[1],
                                   config.display.note_indicator_color[2], config.display.note_indicator_color[3]));
    g_note_indicator.SetAnimationDuration(config.display.note_indicator_animation_duration);
    g_note_indicator.SetBounceDistance(config.display.note_indicator_bounce_distance);
    g_note_indicator.SetEnabled(config.display.note_indicator_enabled);

    // Set global pointers for callbacks
    g_piano = &piano;
    g_renderer = &renderer;
    g_audio_engine = &g_audio;

    // Register piano keyboard with audio engine for MIDI visual feedback
    g_audio.SetPianoKeyboard(&piano);

    // Initialize piano keyboard with config settings
    piano.SetConfig(&config);
    piano.SetNoteIndicator(&g_note_indicator);
    piano.SetAutoLayout(config.keyboard.auto_layout);
    piano.SetKeyboardMargin(config.keyboard.keyboard_margin);
    piano.SetWhiteKeySize(Vec2(config.keyboard.white_key_width, config.keyboard.white_key_height));
    piano.SetBlackKeySize(Vec2(config.keyboard.black_key_width, config.keyboard.black_key_height));
    piano.SetAudioEnabled(config.audio.audio_enabled);

    // Initialize cheat tool settings
    piano.SetMultioctaveEnabled(config.cheat_tool.multioctave_enabled);
    piano.SetMultioctaveCount(config.cheat_tool.multioctave_count);
    piano.SetMultioctaveDistance(config.cheat_tool.multioctave_distance);

    // Set audio settings from config
    g_audio.SetVolume(config.audio.volume);

    // Clamp polyphony to reasonable values to prevent excessive memory usage
    int clamped_polyphony = std::max(1, std::min(5000, config.audio.polyphony));
    if (clamped_polyphony != config.audio.polyphony) {
        std::cout << "Warning: Polyphony value " << config.audio.polyphony
                  << " clamped to " << clamped_polyphony << " to prevent excessive memory usage." << std::endl;
        config.audio.polyphony = clamped_polyphony;
        g_config.AutoSave(); // Save the corrected value
    }
    g_audio.SetMaxPolyphony(clamped_polyphony);

    // Load soundfonts from config
    g_config.LoadSoundFontsToManager(g_audio.GetSoundFontManager());

    // Load all soundfonts and apply them
    g_audio.GetSoundFontManager().LoadAllSoundFonts();
    g_audio.ApplySoundFonts();

    // Set audio limiter settings from config
    if (g_audio.IsInitialized()) {
        g_audio.SetLimiterEnabled(config.audio.limiter_enabled);
        AudioLimiter* limiter = g_audio.GetAudioLimiter();
        if (limiter) {
            limiter->SetThreshold(config.audio.limiter_threshold);
            limiter->SetReleaseTime(config.audio.limiter_release_time);
            limiter->SetLookAheadTime(config.audio.limiter_lookahead_time);
        }
    }

    // Initialize file browser for soundfont selection
    g_soundfont_browser.SetTitle("Select Soundfont File");
    g_soundfont_browser.SetTypeFilters({".sf2", ".SF2", ".sfz", ".SFZ"});
    g_soundfont_browser.SetWindowSize(800, 600);

    // Initialize file browser for background image selection
    g_image_browser.SetTitle("Select Background Image");
    g_image_browser.SetTypeFilters({".png", ".PNG", ".jpg", ".JPG", ".jpeg", ".JPEG", ".bmp", ".BMP", ".tga", ".TGA", ".gif", ".GIF"});
    g_image_browser.SetWindowSize(800, 600);

    // Main loop
    while (!glfwWindowShouldClose(window)) {
        glfwPollEvents();

        // Start the Dear ImGui frame
        ImGui_ImplOpenGL3_NewFrame();
        ImGui_ImplGlfw_NewFrame();
        ImGui::NewFrame();

        // Main menu bar with custom font
        if (g_font_regular) ImGui::PushFont(g_font_regular);
        if (ImGui::BeginMainMenuBar()) {
            if (ImGui::BeginMenu("View")) {
                if (ImGui::MenuItem("Settings", NULL, &config.display.show_settings)) {
                    g_config.AutoSave();
                }
                if (ImGui::MenuItem("BassMIDI Status", NULL, &config.display.show_bassmidi_status)) {
                    g_config.AutoSave();
                }
                if (ImGui::MenuItem("MIDI Input", NULL, &config.display.show_midi_input)) {
                    g_config.AutoSave();
                }
                if (ImGui::MenuItem("Soundfont Manager", NULL, &config.display.show_soundfont_manager)) {
                    g_config.AutoSave();
                }
                if (ImGui::MenuItem("Debug Info", NULL, &config.display.show_debug)) {
                    g_config.AutoSave();
                }
                if (ImGui::MenuItem("Audio Limiter", NULL, &config.display.show_audio_limiter)) {
                    g_config.AutoSave();
                }
                if (ImGui::MenuItem("Cheat Tool", NULL, &config.cheat_tool.show_cheat_tool)) {
                    g_config.AutoSave();
                }
                ImGui::EndMenu();
            }
            ImGui::EndMainMenuBar();
        }
        if (g_font_regular) ImGui::PopFont();

        // Update piano keyboard
        piano.Update();

        // Update note indicator
        g_note_indicator.Update();

        // Audio health monitoring (check every 60 frames, about once per second at 60fps)
        static int audio_check_counter = 0;
        static bool last_audio_status = true;
        audio_check_counter++;
        if (audio_check_counter >= 60) {
            audio_check_counter = 0;
            if (g_audio.IsInitialized()) {
                bool current_audio_status = g_audio.IsAudioWorking();
                if (last_audio_status && !current_audio_status) {
                    // Audio just stopped working
                    std::cout << "WARNING: Audio problem detected! Consider using panic restart." << std::endl;
                }
                last_audio_status = current_audio_status;
            }
        }

        // Process audio for all engines
        g_audio.ProcessAudio();

        // Handle mouse input for piano keyboard (only if ImGui is not using the mouse)
        ImGuiIO& io = ImGui::GetIO();
        bool should_handle_input = !io.WantCaptureMouse && g_mouse_is_down;
        piano.HandleInput(g_mouse_x, g_mouse_y, should_handle_input);

        // Get window size for debug info
        int display_w, display_h;
        glfwGetFramebufferSize(window, &display_w, &display_h);

        // Update renderer viewport
        renderer.SetViewport(display_w, display_h);

        // Update piano keyboard layout based on window size
        piano.UpdateLayout(display_w, display_h);

        // Settings window
        if (config.display.show_settings) {
            bool prev_show_settings = config.display.show_settings;
            ImGui::Begin("Settings", &config.display.show_settings);
            if (prev_show_settings && !config.display.show_settings) {
                g_config.AutoSave(); // Save when window is closed via X button
            }

            ImGui::Text("Keyboard Settings");
            ImGui::Separator();

            if (ImGui::Checkbox("Auto Layout", &config.keyboard.auto_layout)) {
                piano.SetAutoLayout(config.keyboard.auto_layout);
                g_config.AutoSave();
            }

            if (config.keyboard.auto_layout) {
                if (ImGui::SliderFloat("Keyboard Margin", &config.keyboard.keyboard_margin, 10.0f, 200.0f)) {
                    piano.SetKeyboardMargin(config.keyboard.keyboard_margin);
                    g_config.AutoSave();
                }
            } else {
                if (ImGui::SliderFloat2("White Key Size", &config.keyboard.white_key_width, 10.0f, 50.0f)) {
                    piano.SetWhiteKeySize(Vec2(config.keyboard.white_key_width, config.keyboard.white_key_height));
                    g_config.AutoSave();
                }

                if (ImGui::SliderFloat2("Black Key Size", &config.keyboard.black_key_width, 5.0f, 30.0f)) {
                    piano.SetBlackKeySize(Vec2(config.keyboard.black_key_width, config.keyboard.black_key_height));
                    g_config.AutoSave();
                }
            }

            ImGui::Text("PC Keyboard Input");
            ImGui::Separator();
            ImGui::TextWrapped("You can play notes using your PC keyboard:");
            ImGui::Text("Lower row: Z(A3) X(B3) C(C4) V(D4) B(E4) N(F4) M(G4) ,(A4)");
            ImGui::Text("Black keys: S F G J K L");
            ImGui::Text("Upper row: Q(A4) W(B4) E(C5) R(D5) T(E5) Y(F5) U(G5) I(A5)");
            ImGui::Text("Black keys: 2 4 5 7 8 9 - =");
            ImGui::TextWrapped("Z key = A3 (ラ), C key = Middle C (C4). Piano layout with white and black keys.");

            ImGui::Spacing();
            ImGui::Text("Audio Panic Shortcuts");
            ImGui::Separator();
            ImGui::TextColored(ImVec4(1.0f, 0.8f, 0.0f, 1.0f), "Ctrl+Shift+P: PANIC RESTART (emergency audio restart)");
            ImGui::TextColored(ImVec4(0.8f, 1.0f, 0.0f, 1.0f), "Ctrl+Shift+S: Emergency Stop All Notes");
            ImGui::TextColored(ImVec4(0.0f, 0.8f, 1.0f, 1.0f), "Ctrl+Shift+T: Test Audio (play Middle C)");
            ImGui::Spacing();

            // Use large font for section headers
            if (g_font_large) ImGui::PushFont(g_font_large);
            ImGui::Text("Audio Settings");
            if (g_font_large) ImGui::PopFont();
            ImGui::Separator();

            if (g_audio_initialized) {
                if (ImGui::Checkbox("Enable Audio", &config.audio.audio_enabled)) {
                    piano.SetAudioEnabled(config.audio.audio_enabled);
                    g_config.AutoSave();
                }

                if (ImGui::SliderFloat("Volume", &config.audio.volume, 0.0f, 1.0f)) {
                    g_audio.SetVolume(config.audio.volume);
                    g_config.AutoSave();
                }

                ImGui::Text("Soundfont Settings:");
                ImGui::Separator();

                // Show current soundfont status
                if (g_audio.IsSoundfontLoaded()) {
                    ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "Soundfont loaded:");
                    ImGui::Text("  %s", g_audio.GetCurrentSoundfontPath().c_str());
                } else {
                    ImGui::TextColored(ImVec4(1.0f, 1.0f, 0.0f, 1.0f), "No soundfont loaded");
                    ImGui::Text("Using default MIDI sounds");
                }

                ImGui::Spacing();

                ImGui::Spacing();
                ImGui::Separator();
                ImGui::Text("Audio Test:");

                if (ImGui::Button("Play Test Tone")) {
                    g_audio.PlayTestTone();
                }

                ImGui::SameLine();
                if (ImGui::Button("Show Devices")) {
                    g_audio.PrintDeviceInfo();
                }




                ImGui::Spacing();
                ImGui::Separator();

                // SoundFont Manager button
                if (ImGui::Button("Open SoundFont Manager")) {
                    config.display.show_soundfont_manager = true;
                }

                ImGui::Spacing();
                ImGui::TextWrapped("Note: Place .sf2 or .sfz files in the executable directory or use full paths. Popular soundfonts include FluidR3_GM.sf2, GeneralUser_GS.sf2, or Timbres_Of_Heaven.sf2");

                // BASS FX Settings
                ImGui::Spacing();
                ImGui::Text("BASS FX Audio Effects:");
                ImGui::Separator();

                if (ImGui::Checkbox("Enable BASS FX", &config.audio.bassfx_enabled)) {
                    g_audio.SetBASS_FXEnabled(config.audio.bassfx_enabled);
                    g_config.AutoSave();
                }

                if (config.audio.bassfx_enabled) {
                    ImGui::Indent();

                    // Reverb Effect
                    if (ImGui::Checkbox("Reverb", &config.audio.bassfx_reverb_enabled)) {
                        g_audio.SetReverbEnabled(config.audio.bassfx_reverb_enabled);
                        g_config.AutoSave();
                    }
                    if (config.audio.bassfx_reverb_enabled) {
                        ImGui::SameLine();
                        if (ImGui::SliderFloat("##reverb_mix", &config.audio.bassfx_reverb_mix, 0.0f, 1.0f, "Mix: %.2f")) {
                            g_audio.SetReverbMix(config.audio.bassfx_reverb_mix);
                            g_config.AutoSave();
                        }
                    }

                    // Chorus Effect
                    if (ImGui::Checkbox("Chorus", &config.audio.bassfx_chorus_enabled)) {
                        g_audio.SetChorusEnabled(config.audio.bassfx_chorus_enabled);
                        g_config.AutoSave();
                    }
                    if (config.audio.bassfx_chorus_enabled) {
                        ImGui::SameLine();
                        if (ImGui::SliderFloat("##chorus_mix", &config.audio.bassfx_chorus_mix, 0.0f, 1.0f, "Mix: %.2f")) {
                            g_audio.SetChorusMix(config.audio.bassfx_chorus_mix);
                            g_config.AutoSave();
                        }
                    }

                    // Echo Effect
                    if (ImGui::Checkbox("Echo", &config.audio.bassfx_echo_enabled)) {
                        g_audio.SetEchoEnabled(config.audio.bassfx_echo_enabled);
                        g_config.AutoSave();
                    }
                    if (config.audio.bassfx_echo_enabled) {
                        ImGui::SameLine();
                        if (ImGui::SliderFloat("##echo_mix", &config.audio.bassfx_echo_mix, 0.0f, 1.0f, "Mix: %.2f")) {
                            g_audio.SetEchoMix(config.audio.bassfx_echo_mix);
                            g_config.AutoSave();
                        }
                    }

                    // Compressor Effect
                    if (ImGui::Checkbox("Compressor", &config.audio.bassfx_compressor_enabled)) {
                        g_audio.SetCompressorEnabled(config.audio.bassfx_compressor_enabled);
                        g_config.AutoSave();
                    }
                    if (config.audio.bassfx_compressor_enabled) {
                        ImGui::SameLine();
                        if (ImGui::SliderFloat("##compressor_ratio", &config.audio.bassfx_compressor_ratio, 1.0f, 20.0f, "Ratio: %.1f:1")) {
                            g_audio.SetCompressorRatio(config.audio.bassfx_compressor_ratio);
                            g_config.AutoSave();
                        }
                    }

                    ImGui::Unindent();

                    ImGui::Spacing();
                    ImGui::TextWrapped("Note: BASS FX effects are applied to the MIDI audio output. Enable individual effects and adjust their parameters for different sound characteristics.");
                } else {
                    ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "Enable BASS FX to access audio effects");
                }

            } else {
                ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.5f, 1.0f), "Audio engine not available");
                ImGui::Text("BASS libraries may not be installed");
            }

            ImGui::Text("Display Settings");
            ImGui::Separator();

            // Background mode selection
            ImGui::Text("Background Mode:");
            const char* background_modes[] = {"Solid Color", "Radial Gradient", "Transparent", "Image"};
            if (ImGui::Combo("##background_mode", &config.display.background_mode, background_modes, 4)) {
                g_config.AutoSave();
            }

            ImGui::Spacing();

            // Mode-specific settings
            switch (config.display.background_mode) {
                case 0: // Solid color
                    ImGui::Text("Solid Color Settings:");
                    if (ImGui::ColorEdit3("Background Color", config.display.background_color)) {
                        g_config.AutoSave();
                    }
                    break;

                case 1: // Radial gradient
                    ImGui::Text("Gradient Settings:");
                    if (ImGui::ColorEdit3("Center Color", config.display.gradient_center_color)) {
                        g_config.AutoSave();
                    }
                    if (ImGui::ColorEdit3("Edge Color", config.display.gradient_edge_color)) {
                        g_config.AutoSave();
                    }
                    break;

                case 2: // Transparent background
                    ImGui::Text("Transparency Settings:");
                    if (ImGui::SliderFloat("Transparency Level", &config.display.transparency_alpha, 0.0f, 1.0f, "%.2f")) {
                        g_config.AutoSave();
                    }
                    ImGui::TextWrapped("0.0 = Fully transparent, 1.0 = Opaque");

                    ImGui::Spacing();
                    ImGui::Separator();
                    ImGui::Text("Window Transparency:");
                    if (ImGui::Checkbox("Enable Transparent Framebuffer", &config.window.transparent_framebuffer)) {
                        g_config.AutoSave();
                        ImGui::TextColored(ImVec4(1.0f, 1.0f, 0.0f, 1.0f), "Note: Restart required for this change to take effect.");
                    }
                    ImGui::TextWrapped("Enable this for true window transparency. Requires application restart.");
                    break;

                case 3: // Image background
                    ImGui::Text("Image Background Settings:");

                    // Image path input
                    static char image_path_buffer[512];
                    static std::string last_image_path;

                    // Update buffer if config path has changed
                    if (last_image_path != config.display.background_image_path) {
                        strncpy(image_path_buffer, config.display.background_image_path.c_str(), sizeof(image_path_buffer) - 1);
                        image_path_buffer[sizeof(image_path_buffer) - 1] = '\0';
                        last_image_path = config.display.background_image_path;
                    }

                    ImGui::Text("Image File Path:");
                    ImGui::PushItemWidth(-120);
                    if (ImGui::InputText("##image_path", image_path_buffer, sizeof(image_path_buffer))) {
                        config.display.background_image_path = std::string(image_path_buffer);
                        last_image_path = config.display.background_image_path;
                        g_config.AutoSave();
                    }
                    ImGui::PopItemWidth();

                    ImGui::SameLine();
                    if (ImGui::Button("Browse Image...")) {
                        g_image_browser.Open();
                    }

                    // Show image status
                    ImGui::Spacing();
                    if (config.display.background_image_path.empty()) {
                        ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "No image selected");
                    } else {
                        // Check if file exists
                        std::ifstream file_check(config.display.background_image_path);
                        if (file_check.good()) {
                            ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "Image file found");
                        } else {
                            ImGui::TextColored(ImVec4(1.0f, 0.0f, 0.0f, 1.0f), "Image file not found");
                        }
                        file_check.close();
                    }

                    // Image opacity
                    if (ImGui::SliderFloat("Image Opacity", &config.display.background_image_opacity, 0.0f, 1.0f, "%.2f")) {
                        g_config.AutoSave();
                    }

                    // Scale mode
                    ImGui::Text("Scale Mode:");
                    const char* scale_modes[] = {"Stretch", "Fit", "Fill", "Tile"};
                    if (ImGui::Combo("##scale_mode", &config.display.background_image_scale_mode, scale_modes, 4)) {
                        g_config.AutoSave();
                    }

                    ImGui::Spacing();
                    ImGui::TextWrapped("Supported formats: PNG, JPEG, BMP, TGA, GIF, PSD, HDR, PIC");
                    ImGui::TextWrapped("Scale modes:");
                    ImGui::BulletText("Stretch: Stretch image to fill window (may distort)");
                    ImGui::BulletText("Fit: Scale image to fit within window (maintains aspect ratio)");
                    ImGui::BulletText("Fill: Scale image to fill window (maintains aspect ratio, may crop)");
                    ImGui::BulletText("Tile: Repeat image to fill window");
                    break;
            }

            ImGui::Text("Windows");
            ImGui::Separator();

            if (ImGui::Checkbox("Show Debug Window", &config.display.show_debug)) {
                g_config.AutoSave();
            }
            if (ImGui::Checkbox("Show BassMIDI Status", &config.display.show_bassmidi_status)) {
                g_config.AutoSave();
            }
            if (ImGui::Checkbox("Show MIDI Input", &config.display.show_midi_input)) {
                g_config.AutoSave();
            }
            if (ImGui::Checkbox("Show Audio Limiter", &config.display.show_audio_limiter)) {
                g_config.AutoSave();
            }

            ImGui::Spacing();
            ImGui::Text("Graphics Settings");
            ImGui::Separator();

            if (ImGui::Checkbox("Enable VSync", &config.window.vsync_enabled)) {
                glfwSwapInterval(config.window.vsync_enabled ? 1 : 0);
                g_config.AutoSave();
            }
            ImGui::SameLine();
            ImGui::TextDisabled("(?)");
            if (ImGui::IsItemHovered()) {
                ImGui::SetTooltip("Vertical synchronization limits frame rate to monitor refresh rate.\nDisabling may increase performance but can cause screen tearing.");
            }

            if (ImGui::Button("Reset All Keys")) {
                for (int i = 0; i < 128; ++i) {
                    piano.SetKeyPressed(i, false);
                }
            }

            // Quick Audio Panic Section
            ImGui::Spacing();
            ImGui::Separator();
            ImGui::Text("Quick Audio Controls");

            if (g_audio.IsInitialized()) {
                // Audio status indicator
                bool audio_working = g_audio.IsAudioWorking();
                if (audio_working) {
                    ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "Audio: OK");
                } else {
                    ImGui::TextColored(ImVec4(1.0f, 0.0f, 0.0f, 1.0f), "Audio: ERROR");
                }

                ImGui::SameLine();
                if (ImGui::Button("Stop All Notes")) {
                    g_audio.EmergencyStopAllNotes();
                }

                ImGui::SameLine();
                ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.8f, 0.2f, 0.2f, 1.0f));
                if (ImGui::Button("PANIC")) {
                    g_audio.PanicRestart();
                }
                ImGui::PopStyleColor();
                if (ImGui::IsItemHovered()) {
                    ImGui::SetTooltip("Emergency restart audio engine");
                }
            } else {
                ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.0f, 1.0f), "Audio: Not Initialized");
                if (ImGui::Button("Initialize Audio")) {
                    g_audio.Initialize();
                }
            }

            // Handle image file browser
            g_image_browser.Display();
            if (g_image_browser.HasSelected()) {
                config.display.background_image_path = g_image_browser.GetSelected().string();
                g_config.AutoSave();
                g_image_browser.ClearSelected();
            }

            ImGui::Spacing();
            ImGui::Text("Note Indicator Settings:");
            ImGui::Separator();

            if (ImGui::Checkbox("Enable Note Indicator", &config.display.note_indicator_enabled)) {
                g_note_indicator.SetEnabled(config.display.note_indicator_enabled);
                g_config.AutoSave();
            }

            if (config.display.note_indicator_enabled) {
                if (ImGui::ColorEdit4("Indicator Color", config.display.note_indicator_color)) {
                    g_note_indicator.SetColor(Color(config.display.note_indicator_color[0],
                                                   config.display.note_indicator_color[1],
                                                   config.display.note_indicator_color[2],
                                                   config.display.note_indicator_color[3]));
                    g_config.AutoSave();
                }

                if (ImGui::SliderFloat2("Size", config.display.note_indicator_size, 20.0f, 200.0f)) {
                    g_note_indicator.SetSize(Vec2(config.display.note_indicator_size[0],
                                                 config.display.note_indicator_size[1]));
                    g_config.AutoSave();
                }

                if (ImGui::SliderFloat2("Position", config.display.note_indicator_position, 0.0f, 500.0f)) {
                    g_note_indicator.SetPosition(Vec2(config.display.note_indicator_position[0],
                                                     config.display.note_indicator_position[1]));
                    g_config.AutoSave();
                }

                if (ImGui::SliderFloat("Animation Duration (ms)", &config.display.note_indicator_animation_duration, 50.0f, 1000.0f)) {
                    g_note_indicator.SetAnimationDuration(config.display.note_indicator_animation_duration);
                    g_config.AutoSave();
                }

                if (ImGui::SliderFloat("Bounce Distance", &config.display.note_indicator_bounce_distance, 1.0f, 50.0f)) {
                    g_note_indicator.SetBounceDistance(config.display.note_indicator_bounce_distance);
                    g_config.AutoSave();
                }
            }

            ImGui::End();
        }

        // Piano keyboard is now rendered directly with OpenGL (see below)

        // Debug window
        if (config.display.show_debug) {
            bool prev_show_debug = config.display.show_debug;
            ImGui::Begin("Debug Info", &config.display.show_debug);
            if (prev_show_debug && !config.display.show_debug) {
                g_config.AutoSave(); // Save when window is closed via X button
            }
            ImGui::Text("Application average %.3f ms/frame (%.1f FPS)",
                       1000.0f / ImGui::GetIO().Framerate, ImGui::GetIO().Framerate);
            ImGui::Text("Pressed keys: %d", piano.GetPressedKeyCount());

            auto pressed_keys = piano.GetPressedKeys();
            if (!pressed_keys.empty()) {
                ImGui::Text("Active notes:");
                for (int note : pressed_keys) {
                    ImGui::SameLine();
                    ImGui::Text("%d", note);
                }
            }

            ImGui::Text("Rendered Blips: %d", piano.GetTotalBlipCount());
            ImGui::Text("Mouse Position: (%.1f, %.1f)", ImGui::GetIO().MousePos.x, ImGui::GetIO().MousePos.y);
            ImGui::Text("Window Size: (%d, %d)", display_w, display_h);

            ImGui::Separator();
            ImGui::Text("Audio Engine: %s", g_audio.IsInitialized() ? "Initialized" : "Not Available");
            if (g_audio.IsInitialized()) {
                ImGui::Text("Audio Enabled: %s", piano.IsAudioEnabled() ? "Yes" : "No");
                ImGui::Text("Volume: %.2f", g_audio.GetVolume());
                ImGui::Text("Soundfont: %s", g_audio.IsSoundfontLoaded() ?
                    g_audio.GetCurrentSoundfontPath().c_str() : "None (using default sounds)");
                ImGui::Text("Polyphony: %d / %d", g_audio.GetCurrentPolyphony(), g_audio.GetMaxPolyphony());

                // Polyphony bar
                float polyphony_ratio = g_audio.GetMaxPolyphony() > 0 ?
                    (float)g_audio.GetCurrentPolyphony() / (float)g_audio.GetMaxPolyphony() : 0.0f;
                ImGui::ProgressBar(polyphony_ratio, ImVec2(-1, 0),
                    (std::to_string(g_audio.GetCurrentPolyphony()) + " / " + std::to_string(g_audio.GetMaxPolyphony())).c_str());

                // Polyphony setting
                ImGui::Spacing();
                ImGui::Text("Polyphony Settings:");

                // Ensure AudioEngine polyphony matches config (in case it was changed elsewhere)
                if (config.audio.polyphony != g_audio.GetMaxPolyphony()) {
                    g_audio.SetMaxPolyphony(config.audio.polyphony);
                }

                // Use a slider for common values (1-1000) and input field for higher values
                if (ImGui::SliderInt("Max Polyphony", &config.audio.polyphony, 1, 1000)) {
                    if (g_audio.SetMaxPolyphony(config.audio.polyphony)) {
                        std::cout << "Polyphony changed to: " << config.audio.polyphony << std::endl;
                        g_config.AutoSave();
                    }
                }

                // Input field for custom values up to 5,000
                ImGui::Text("Custom Value (1-5000):");
                if (ImGui::InputInt("##polyphony_input", &config.audio.polyphony, 1, 100)) {
                    // Clamp the value to valid range
                    if (config.audio.polyphony < 1) config.audio.polyphony = 1;
                    if (config.audio.polyphony > 5000) config.audio.polyphony = 5000;

                    if (g_audio.SetMaxPolyphony(config.audio.polyphony)) {
                        std::cout << "Polyphony changed to: " << config.audio.polyphony << std::endl;
                        g_config.AutoSave();
                    }
                }

                // Preset buttons for common polyphony values
                ImGui::Text("Presets:");
                if (ImGui::Button("16")) {
                    config.audio.polyphony = 16;
                    g_audio.SetMaxPolyphony(config.audio.polyphony);
                    g_config.AutoSave();
                }
                ImGui::SameLine();
                if (ImGui::Button("32")) {
                    config.audio.polyphony = 32;
                    g_audio.SetMaxPolyphony(config.audio.polyphony);
                    g_config.AutoSave();
                }
                ImGui::SameLine();
                if (ImGui::Button("64")) {
                    config.audio.polyphony = 64;
                    g_audio.SetMaxPolyphony(config.audio.polyphony);
                    g_config.AutoSave();
                }
                ImGui::SameLine();
                if (ImGui::Button("128")) {
                    config.audio.polyphony = 128;
                    g_audio.SetMaxPolyphony(config.audio.polyphony);
                    g_config.AutoSave();
                }
                ImGui::SameLine();
                if (ImGui::Button("256")) {
                    config.audio.polyphony = 256;
                    g_audio.SetMaxPolyphony(config.audio.polyphony);
                    g_config.AutoSave();
                }

                // Second row of presets
                if (ImGui::Button("512")) {
                    config.audio.polyphony = 512;
                    g_audio.SetMaxPolyphony(config.audio.polyphony);
                    g_config.AutoSave();
                }
                ImGui::SameLine();
                if (ImGui::Button("1000")) {
                    config.audio.polyphony = 1000;
                    g_audio.SetMaxPolyphony(config.audio.polyphony);
                    g_config.AutoSave();
                }
                ImGui::SameLine();
                if (ImGui::Button("2000")) {
                    config.audio.polyphony = 2000;
                    g_audio.SetMaxPolyphony(config.audio.polyphony);
                    g_config.AutoSave();
                }

                // Third row of presets
                if (ImGui::Button("3000")) {
                    config.audio.polyphony = 3000;
                    g_audio.SetMaxPolyphony(config.audio.polyphony);
                    g_config.AutoSave();
                }
                ImGui::SameLine();
                if (ImGui::Button("4000")) {
                    config.audio.polyphony = 4000;
                    g_audio.SetMaxPolyphony(config.audio.polyphony);
                    g_config.AutoSave();
                }
                ImGui::SameLine();
                if (ImGui::Button("5000 (Max)")) {
                    config.audio.polyphony = 5000;
                    g_audio.SetMaxPolyphony(config.audio.polyphony);
                    g_config.AutoSave();
                }


            }

            ImGui::End();
        }

        // BassMIDI Status Window
        if (config.display.show_bassmidi_status) {
            bool prev_show_bassmidi_status = config.display.show_bassmidi_status;
            ImGui::Begin("BassMIDI Status", &config.display.show_bassmidi_status);
            if (prev_show_bassmidi_status && !config.display.show_bassmidi_status) {
                g_config.AutoSave(); // Save when window is closed via X button
            }

            if (g_audio.IsInitialized()) {
                // Performance metrics
                ImGui::Text("Performance Metrics");
                ImGui::Separator();

                float cpu_usage = g_audio.GetCPUUsage();
                ImGui::Text("CPU Usage: %.2f%%", cpu_usage);
                ImGui::ProgressBar(cpu_usage / 100.0f, ImVec2(-1, 0),
                    (std::to_string(static_cast<int>(cpu_usage)) + "%").c_str());

                float render_time = g_audio.GetRenderingTime();
                ImGui::Text("Rendering Time: %.3f ms", render_time);

                // Voice and channel information
                ImGui::Spacing();
                ImGui::Text("Voice Information");
                ImGui::Separator();

                int current_voices = g_audio.GetCurrentPolyphony();
                int max_voices = g_audio.GetMaxPolyphony();
                ImGui::Text("Active Voices: %d / %d", current_voices, max_voices);

                float voice_ratio = max_voices > 0 ? (float)current_voices / (float)max_voices : 0.0f;
                ImGui::ProgressBar(voice_ratio, ImVec2(-1, 0),
                    (std::to_string(current_voices) + " / " + std::to_string(max_voices)).c_str());

                int active_channels = g_audio.GetActiveChannels();
                ImGui::Text("Active Channels: %d", active_channels);

                // Audio system information
                ImGui::Spacing();
                ImGui::Text("Audio System Information");
                ImGui::Separator();

                std::string audio_info = g_audio.GetAudioInfo();
                ImGui::TextWrapped("%s", audio_info.c_str());

                // Real-time statistics
                ImGui::Spacing();
                ImGui::Text("Real-time Statistics");
                ImGui::Separator();

                static float cpu_history[100] = {0};
                static int cpu_history_offset = 0;
                cpu_history[cpu_history_offset] = cpu_usage;
                cpu_history_offset = (cpu_history_offset + 1) % 100;

                ImGui::PlotLines("CPU Usage History", cpu_history, 100, cpu_history_offset,
                    "CPU %", 0.0f, 100.0f, ImVec2(0, 80));

                static float voice_history[100] = {0};
                static int voice_history_offset = 0;
                voice_history[voice_history_offset] = voice_ratio * 100.0f;
                voice_history_offset = (voice_history_offset + 1) % 100;

                ImGui::PlotLines("Voice Usage History", voice_history, 100, voice_history_offset,
                    "Voices %", 0.0f, 100.0f, ImVec2(0, 80));

                // Panic and Recovery Section
                ImGui::Spacing();
                ImGui::Text("Panic & Recovery");
                ImGui::Separator();

                // Audio status indicator
                bool audio_working = g_audio.IsAudioWorking();
                if (audio_working) {
                    ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "● Audio Status: Working");
                } else {
                    ImGui::TextColored(ImVec4(1.0f, 0.0f, 0.0f, 1.0f), "● Audio Status: Problem Detected!");
                }

                // Emergency Stop All Notes button
                if (ImGui::Button("Emergency Stop All Notes", ImVec2(-1, 0))) {
                    g_audio.EmergencyStopAllNotes();
                }
                if (ImGui::IsItemHovered()) {
                    ImGui::SetTooltip("Immediately stop all playing notes and reset MIDI channels");
                }

                // Panic Restart button
                ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.8f, 0.2f, 0.2f, 1.0f));
                ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(1.0f, 0.3f, 0.3f, 1.0f));
                ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0.6f, 0.1f, 0.1f, 1.0f));

                if (ImGui::Button("PANIC RESTART", ImVec2(-1, 0))) {
                    static bool confirm_panic = false;
                    if (!confirm_panic) {
                        confirm_panic = true;
                        ImGui::OpenPopup("Confirm Panic Restart");
                    }
                }
                ImGui::PopStyleColor(3);

                if (ImGui::IsItemHovered()) {
                    ImGui::SetTooltip("Emergency restart of the entire audio engine\n"
                                    "Use this when audio stops working completely\n"
                                    "WARNING: This will temporarily stop all audio!");
                }

                // Confirmation popup for panic restart
                if (ImGui::BeginPopupModal("Confirm Panic Restart", nullptr, ImGuiWindowFlags_AlwaysAutoResize)) {
                    ImGui::Text("Are you sure you want to restart the audio engine?");
                    ImGui::Text("This will temporarily stop all audio and may take a few seconds.");
                    ImGui::Separator();

                    if (ImGui::Button("Yes, Restart Audio Engine", ImVec2(200, 0))) {
                        if (g_audio.PanicRestart()) {
                            ImGui::CloseCurrentPopup();
                        }
                    }
                    ImGui::SameLine();
                    if (ImGui::Button("Cancel", ImVec2(100, 0))) {
                        ImGui::CloseCurrentPopup();
                    }
                    ImGui::EndPopup();
                }

                // Test tone button
                if (ImGui::Button("Test Audio", ImVec2(-1, 0))) {
                    g_audio.PlayTestTone();
                }
                if (ImGui::IsItemHovered()) {
                    ImGui::SetTooltip("Play a test tone (Middle C) to check if audio is working");
                }

            } else {
                ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.5f, 1.0f), "Audio engine not initialized");
                ImGui::Text("BassMIDI status information is not available");

                // Show panic restart option even when not initialized
                ImGui::Spacing();
                ImGui::Text("Recovery Options");
                ImGui::Separator();

                if (ImGui::Button("Try Initialize Audio Engine", ImVec2(-1, 0))) {
                    if (g_audio.Initialize()) {
                        std::cout << "Audio engine initialized successfully!" << std::endl;
                    } else {
                        std::cout << "Failed to initialize audio engine" << std::endl;
                    }
                }
            }

            ImGui::End();
        }

        // MIDI Input Window
        if (config.display.show_midi_input) {
            bool prev_show_midi_input = config.display.show_midi_input;
            ImGui::Begin("MIDI Input", &config.display.show_midi_input);
            if (prev_show_midi_input && !config.display.show_midi_input) {
                g_config.AutoSave(); // Save when window is closed via X button
            }

            if (g_audio.IsInitialized()) {
                // MIDI Input Type Selection
                ImGui::Text("MIDI Input Type");
                ImGui::Separator();

#ifdef _WIN32
                // Windows: Use Windows MIDI (winmm), BASS MIDI, or External Process MIDI
                if (ImGui::RadioButton("BASS MIDI Input", !config.midi.use_alsa_midi && !config.midi.use_ext_process_midi)) {
                    if (config.midi.use_alsa_midi || config.midi.use_ext_process_midi) {
                        // Switch from other inputs to BASS
                        g_audio.CloseWinMMMIDIDevice();
                        g_audio.StopExtProcessMIDI();
                        config.midi.selected_alsa_midi_device = -1;
                        config.midi.use_alsa_midi = false;
                        config.midi.use_ext_process_midi = false;
                        g_config.AutoSave();
                    }
                }
                if (ImGui::RadioButton("Windows MIDI Input", config.midi.use_alsa_midi && !config.midi.use_ext_process_midi)) {
                    if (!config.midi.use_alsa_midi || config.midi.use_ext_process_midi) {
                        // Switch from other inputs to Windows MIDI
                        g_audio.CloseMIDIInputDevice();
                        g_audio.StopExtProcessMIDI();
                        config.midi.selected_midi_device = -1;
                        config.midi.use_alsa_midi = true;
                        config.midi.use_ext_process_midi = false;
                        g_config.AutoSave();
                    }
                }
                if (ImGui::RadioButton("External Process MIDI", config.midi.use_ext_process_midi)) {
                    if (!config.midi.use_ext_process_midi) {
                        // Switch from other inputs to External Process MIDI
                        g_audio.CloseMIDIInputDevice();
                        g_audio.CloseWinMMMIDIDevice();
                        config.midi.selected_midi_device = -1;
                        config.midi.selected_alsa_midi_device = -1;
                        config.midi.use_alsa_midi = false;
                        config.midi.use_ext_process_midi = true;
                        g_config.AutoSave();
                    }
                }
#else
                // Linux: Use ALSA MIDI, BASS MIDI, or External Process MIDI
                if (ImGui::RadioButton("BASS MIDI Input", !config.midi.use_alsa_midi && !config.midi.use_ext_process_midi)) {
                    if (config.midi.use_alsa_midi || config.midi.use_ext_process_midi) {
                        // Switch from other inputs to BASS
                        g_audio.CloseALSAMIDIDevice();
                        g_audio.StopExtProcessMIDI();
                        config.midi.selected_alsa_midi_device = -1;
                        config.midi.use_alsa_midi = false;
                        config.midi.use_ext_process_midi = false;
                        g_config.AutoSave();
                    }
                }
                if (ImGui::RadioButton("ALSA MIDI Input", config.midi.use_alsa_midi && !config.midi.use_ext_process_midi)) {
                    if (!config.midi.use_alsa_midi || config.midi.use_ext_process_midi) {
                        // Switch from other inputs to ALSA
                        g_audio.CloseMIDIInputDevice();
                        g_audio.StopExtProcessMIDI();
                        config.midi.selected_midi_device = -1;
                        config.midi.use_alsa_midi = true;
                        config.midi.use_ext_process_midi = false;
                        g_config.AutoSave();
                    }
                }
                if (ImGui::RadioButton("External Process MIDI", config.midi.use_ext_process_midi)) {
                    if (!config.midi.use_ext_process_midi) {
                        // Switch from other inputs to External Process MIDI
                        g_audio.CloseMIDIInputDevice();
                        g_audio.CloseALSAMIDIDevice();
                        config.midi.selected_midi_device = -1;
                        config.midi.selected_alsa_midi_device = -1;
                        config.midi.use_alsa_midi = false;
                        config.midi.use_ext_process_midi = true;
                        g_config.AutoSave();
                    }
                }
#endif

                ImGui::Spacing();
                ImGui::Separator();

                if (!config.midi.use_alsa_midi && !config.midi.use_ext_process_midi) {
                    // BASS MIDI Input Device Selection
                    ImGui::Text("BASS MIDI Input Devices");
                    ImGui::Separator();

                    std::vector<std::string> midi_devices = g_audio.GetMIDIInputDevices();

                    if (midi_devices.empty()) {
                        ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.5f, 1.0f), "No BASS MIDI input devices found");
                        ImGui::Text("Make sure MIDI devices are connected and drivers are installed");
                    } else {
                        ImGui::Text("Available BASS MIDI Input Devices:");

                        for (size_t i = 0; i < midi_devices.size(); i++) {
                            bool is_selected = (config.midi.selected_midi_device == static_cast<int>(i));

                            // Create unique ID for each selectable item
                            std::string device_label = midi_devices[i] + "##bass_midi_device_" + std::to_string(i);

                            if (ImGui::Selectable(device_label.c_str(), is_selected)) {
                                if (config.midi.selected_midi_device != static_cast<int>(i)) {
                                    config.midi.selected_midi_device = static_cast<int>(i);
                                    if (g_audio.OpenMIDIInputDevice(config.midi.selected_midi_device)) {
                                        std::cout << "Opened BASS MIDI input device: " << midi_devices[i] << std::endl;
                                        g_config.AutoSave();
                                    } else {
                                        std::cerr << "Failed to open BASS MIDI input device: " << midi_devices[i] << std::endl;
                                        config.midi.selected_midi_device = -1;
                                    }
                                }
                            }

                            if (is_selected) {
                                ImGui::SetItemDefaultFocus();
                            }
                        }
                    }
                } else if (config.midi.use_ext_process_midi) {
                    // External Process MIDI Configuration
                    ImGui::Text("External Process MIDI Configuration");
                    ImGui::Separator();

                    // Executable path input
                    static char executable_path_buffer[512] = "";
                    static bool buffer_initialized = false;
                    if (!buffer_initialized || strlen(executable_path_buffer) == 0) {
                        strncpy(executable_path_buffer, config.midi.ext_process_executable_path.c_str(), sizeof(executable_path_buffer) - 1);
                        executable_path_buffer[sizeof(executable_path_buffer) - 1] = '\0';
                        buffer_initialized = true;
                    }

                    if (ImGui::InputText("Executable Path", executable_path_buffer, sizeof(executable_path_buffer))) {
                        config.midi.ext_process_executable_path = std::string(executable_path_buffer);
                        g_config.AutoSave();
                    }

                    // Arguments input
                    static char arguments_buffer[1024] = "";
                    static bool args_buffer_initialized = false;
                    if (!args_buffer_initialized || strlen(arguments_buffer) == 0) {
                        strncpy(arguments_buffer, config.midi.ext_process_arguments.c_str(), sizeof(arguments_buffer) - 1);
                        arguments_buffer[sizeof(arguments_buffer) - 1] = '\0';
                        args_buffer_initialized = true;
                    }

                    if (ImGui::InputText("Arguments", arguments_buffer, sizeof(arguments_buffer))) {
                        config.midi.ext_process_arguments = std::string(arguments_buffer);
                        g_config.AutoSave();
                    }

                    ImGui::Spacing();

                    // Control buttons
                    if (!g_audio.IsExtProcessMIDIActive()) {
                        if (ImGui::Button("Start External Process MIDI")) {
                            if (!config.midi.ext_process_executable_path.empty()) {
                                std::vector<std::string> args;
                                if (!config.midi.ext_process_arguments.empty()) {
                                    // Parse arguments properly, handling quotes and spaces
                                    std::string arg_string = config.midi.ext_process_arguments;
                                    std::istringstream iss(arg_string);
                                    std::string token;
                                    bool in_quotes = false;
                                    std::string current_arg;

                                    for (size_t i = 0; i < arg_string.length(); ++i) {
                                        char c = arg_string[i];

                                        if (c == '\'' || c == '"') {
                                            in_quotes = !in_quotes;
                                        } else if (c == ' ' && !in_quotes) {
                                            if (!current_arg.empty()) {
                                                args.push_back(current_arg);
                                                current_arg.clear();
                                            }
                                        } else {
                                            current_arg += c;
                                        }
                                    }

                                    if (!current_arg.empty()) {
                                        args.push_back(current_arg);
                                    }
                                }

                                if (g_audio.StartExtProcessMIDI(config.midi.ext_process_executable_path, args)) {
                                    std::cout << "Started External Process MIDI: " << config.midi.ext_process_executable_path << std::endl;
                                    config.midi.ext_process_enabled = true;
                                    g_config.AutoSave();
                                } else {
                                    std::cerr << "Failed to start External Process MIDI" << std::endl;
                                }
                            } else {
                                ImGui::OpenPopup("Error##ExtProcessPath");
                            }
                        }
                    } else {
                        if (ImGui::Button("Stop External Process MIDI")) {
                            g_audio.StopExtProcessMIDI();
                            config.midi.ext_process_enabled = false;
                            g_config.AutoSave();
                        }
                    }

                    // Error popup for empty path
                    if (ImGui::BeginPopupModal("Error##ExtProcessPath", NULL, ImGuiWindowFlags_AlwaysAutoResize)) {
                        ImGui::Text("Please specify an executable path first.");
                        if (ImGui::Button("OK")) {
                            ImGui::CloseCurrentPopup();
                        }
                        ImGui::EndPopup();
                    }

                    ImGui::Spacing();

                    // Stderr log display
                    std::string stderr_log = g_audio.GetExtProcessMIDIStderrLog();
                    if (!stderr_log.empty()) {
                        ImGui::Text("Process Error Log:");
                        ImGui::BeginChild("StderrLog", ImVec2(0, 100), true);
                        ImGui::TextWrapped("%s", stderr_log.c_str());
                        ImGui::EndChild();

                        if (ImGui::Button("Clear Log")) {
                            g_audio.ClearExtProcessMIDIStderrLog();
                        }
                    }

                } else {
#ifdef _WIN32
                    // Windows MIDI Input Device Selection
                    ImGui::Text("Windows MIDI Input Devices");
                    ImGui::Separator();

                    std::vector<WinMMMIDIDevice> winmm_devices = g_audio.GetWinMMMIDIDevices();

                    if (winmm_devices.empty()) {
                        ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.5f, 1.0f), "No Windows MIDI input devices found");
                        ImGui::Text("Make sure MIDI devices are connected and drivers are installed");
                    } else {
                        ImGui::Text("Available Windows MIDI Input Devices:");

                        for (size_t i = 0; i < winmm_devices.size(); i++) {
                            bool is_selected = (config.midi.selected_alsa_midi_device == static_cast<int>(i));

                            // Create unique ID for each selectable item
                            std::string device_label = winmm_devices[i].name + "##winmm_midi_device_" + std::to_string(i);

                            if (ImGui::Selectable(device_label.c_str(), is_selected)) {
                                if (config.midi.selected_alsa_midi_device != static_cast<int>(i)) {
                                    config.midi.selected_alsa_midi_device = static_cast<int>(i);
                                    if (g_audio.OpenWinMMMIDIDevice(winmm_devices[i].device_id)) {
                                        std::cout << "Opened Windows MIDI input device: " << winmm_devices[i].name << std::endl;
                                        g_config.AutoSave();
                                    } else {
                                        std::cerr << "Failed to open Windows MIDI input device: " << winmm_devices[i].name << std::endl;
                                        config.midi.selected_alsa_midi_device = -1;
                                    }
                                }
                            }

                            if (is_selected) {
                                ImGui::SetItemDefaultFocus();
                            }
                        }
                    }
#else
                    // ALSA MIDI Input Device Selection
                    ImGui::Text("ALSA MIDI Input Devices");
                    ImGui::Separator();

                    std::vector<ALSAMIDIDevice> alsa_devices = g_audio.GetALSAMIDIDevices();

                    if (alsa_devices.empty()) {
                        ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.5f, 1.0f), "No ALSA MIDI input devices found");
                        ImGui::Text("Make sure MIDI devices are connected and ALSA is properly configured");
                    } else {
                        ImGui::Text("Available ALSA MIDI Input Devices:");

                        for (size_t i = 0; i < alsa_devices.size(); i++) {
                            bool is_selected = (config.midi.selected_alsa_midi_device == static_cast<int>(i));

                            // Create unique ID for each selectable item
                            std::string device_label = alsa_devices[i].full_name + "##alsa_midi_device_" + std::to_string(i);

                            if (ImGui::Selectable(device_label.c_str(), is_selected)) {
                                if (config.midi.selected_alsa_midi_device != static_cast<int>(i)) {
                                    config.midi.selected_alsa_midi_device = static_cast<int>(i);
                                    if (g_audio.OpenALSAMIDIDevice(alsa_devices[i].client_id, alsa_devices[i].port_id)) {
                                        std::cout << "Opened ALSA MIDI input device: " << alsa_devices[i].full_name << std::endl;
                                        g_config.AutoSave();
                                    } else {
                                        std::cerr << "Failed to open ALSA MIDI input device: " << alsa_devices[i].full_name << std::endl;
                                        config.midi.selected_alsa_midi_device = -1;
                                    }
                                }
                            }

                            if (is_selected) {
                                ImGui::SetItemDefaultFocus();
                            }
                        }
                    }
#endif
                }

                ImGui::Spacing();
                ImGui::Separator();

                // Current MIDI Input Status
                ImGui::Text("MIDI Input Status");
                ImGui::Separator();

                if (config.midi.use_ext_process_midi) {
                    // External Process MIDI Status
                    if (g_audio.IsExtProcessMIDIActive()) {
                        ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "External Process MIDI: ACTIVE");
                        ImGui::Text("Executable: %s", config.midi.ext_process_executable_path.c_str());
                        if (!config.midi.ext_process_arguments.empty()) {
                            ImGui::Text("Arguments: %s", config.midi.ext_process_arguments.c_str());
                        }
                    } else {
                        ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.5f, 1.0f), "External Process MIDI: INACTIVE");
                        ImGui::Text("No external process is currently running");
                    }
                } else if (!config.midi.use_alsa_midi) {
                    // BASS MIDI Status
                    if (g_audio.IsMIDIInputOpen()) {
                        ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "BASS MIDI Input: ACTIVE");
                        ImGui::Text("Device: %s", g_audio.GetCurrentMIDIInputDevice().c_str());

                        if (ImGui::Button("Close BASS MIDI Input")) {
                            g_audio.CloseMIDIInputDevice();
                            config.midi.selected_midi_device = -1;
                        }
                    } else {
                        ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.5f, 1.0f), "BASS MIDI Input: INACTIVE");
                        ImGui::Text("No BASS MIDI input device is currently open");
                    }
                } else {
#ifdef _WIN32
                    // Windows MIDI Status
                    if (g_audio.IsWinMMMIDIOpen()) {
                        ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "Windows MIDI Input: ACTIVE");
                        WinMMMIDIDevice current_device = g_audio.GetCurrentWinMMMIDIDevice();
                        ImGui::Text("Device: %s", current_device.name.c_str());

                        if (ImGui::Button("Close Windows MIDI Input")) {
                            g_audio.CloseWinMMMIDIDevice();
                            config.midi.selected_alsa_midi_device = -1;
                        }
                    } else {
                        ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.5f, 1.0f), "Windows MIDI Input: INACTIVE");
                        ImGui::Text("No Windows MIDI input device is currently open");
                    }
#else
                    // ALSA MIDI Status
                    if (g_audio.IsALSAMIDIOpen()) {
                        ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "ALSA MIDI Input: ACTIVE");
                        ALSAMIDIDevice current_device = g_audio.GetCurrentALSAMIDIDevice();
                        ImGui::Text("Device: %s", current_device.full_name.c_str());

                        if (ImGui::Button("Close ALSA MIDI Input")) {
                            g_audio.CloseALSAMIDIDevice();
                            config.midi.selected_alsa_midi_device = -1;
                        }
                    } else {
                        ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.5f, 1.0f), "ALSA MIDI Input: INACTIVE");
                        ImGui::Text("No ALSA MIDI input device is currently open");
                    }
#endif
                }

                ImGui::Spacing();
                ImGui::Separator();

                // MIDI Color Settings
                ImGui::Text("MIDI Color Settings");
                ImGui::Separator();

                const char* color_types[] = { "Channel Colors", "Rainbow", "Key Rainbow", "Key Rainbow Reverse", "Velocity Rainbow" };
                if (ImGui::Combo("Color Type", &config.midi.color_type, color_types, IM_ARRAYSIZE(color_types))) {
                    g_config.AutoSave();
                }

                // Show description for selected color type
                switch (config.midi.color_type) {
                    case 0:
                        ImGui::TextWrapped("Channel Colors: Each MIDI channel (0-15) has a distinct color.");
                        break;
                    case 1:
                        ImGui::TextWrapped("Rainbow: Continuously cycling rainbow colors (updates every 1ms).");
                        break;
                    case 2:
                        ImGui::TextWrapped("Key Rainbow: Each MIDI note (0-127) has a rainbow color that shifts forward every 50ms.");
                        break;
                    case 3:
                        ImGui::TextWrapped("Key Rainbow Reverse: Each MIDI note (0-127) has a rainbow color that shifts backward every 50ms.");
                        break;
                    case 4:
                        ImGui::TextWrapped("Velocity Rainbow: Color is determined by MIDI velocity (0-127). Low velocity = dark blue, high velocity = bright red.");
                        break;
                }

                ImGui::Spacing();
                ImGui::Separator();

                // MIDI Input Instructions
                ImGui::Text("Instructions");
                ImGui::Separator();
                ImGui::TextWrapped("1. Connect your MIDI keyboard or controller, OR configure an external process");
#ifdef _WIN32
                ImGui::TextWrapped("2. Choose between BASS MIDI Input (cross-platform), Windows MIDI Input (native Windows), or External Process MIDI");
#else
                ImGui::TextWrapped("2. Choose between BASS MIDI Input (cross-platform), ALSA MIDI Input (native Linux), or External Process MIDI");
#endif
                ImGui::TextWrapped("3. For hardware MIDI: Select a device from the list above");
                ImGui::TextWrapped("4. For External Process MIDI: Configure executable path and arguments, then start the process");
                ImGui::TextWrapped("5. Play notes on your MIDI device to hear them through the piano");
                ImGui::TextWrapped("6. MIDI Note On/Off messages will be automatically converted to piano notes");
                ImGui::Spacing();
#ifdef _WIN32
                ImGui::TextWrapped("Note: Windows MIDI Input provides better Windows compatibility and lower latency for native Windows MIDI devices.");
#else
                ImGui::TextWrapped("Note: ALSA MIDI Input provides better Linux compatibility and lower latency for native ALSA devices.");
#endif
                ImGui::TextWrapped("External Process MIDI allows integration with custom MIDI applications that output MIDI data in CSV format (status,data1,data2 in hex).");

            } else {
                ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.5f, 1.0f), "Audio engine not initialized");
                ImGui::Text("MIDI input requires the audio engine to be running");
            }

            ImGui::End();
        }

        // Audio Limiter Debug Window
        if (config.display.show_audio_limiter) {
            bool prev_show_audio_limiter = config.display.show_audio_limiter;
            ImGui::Begin("Audio Limiter", &config.display.show_audio_limiter);
            if (prev_show_audio_limiter && !config.display.show_audio_limiter) {
                g_config.AutoSave(); // Save when window is closed via X button
            }

            if (g_audio.IsInitialized()) {
                AudioLimiter* limiter = g_audio.GetAudioLimiter();

                // Enable/Disable Limiter
                ImGui::Text("Audio Limiter Control");
                ImGui::Separator();

                if (ImGui::Checkbox("Enable Audio Limiter", &config.audio.limiter_enabled)) {
                    g_audio.SetLimiterEnabled(config.audio.limiter_enabled);
                    g_config.AutoSave();
                }

                if (config.audio.limiter_enabled) {
                    ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "Status: ACTIVE");
                } else {
                    ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.5f, 1.0f), "Status: DISABLED");
                }

                ImGui::Spacing();
                ImGui::Separator();

                // Limiter Parameters
                ImGui::Text("Limiter Parameters");
                ImGui::Separator();

                // Threshold
                if (ImGui::SliderFloat("Threshold (dB)", &config.audio.limiter_threshold, -60.0f, 0.0f, "%.1f dB")) {
                    limiter->SetThreshold(config.audio.limiter_threshold);
                    g_config.AutoSave();
                }
                ImGui::SameLine();
                if (ImGui::Button("Reset##threshold")) {
                    config.audio.limiter_threshold = -6.0f;
                    limiter->SetThreshold(config.audio.limiter_threshold);
                    g_config.AutoSave();
                }

                // Release Time
                if (ImGui::SliderFloat("Release (ms)", &config.audio.limiter_release_time, 5.0f, 200.0f, "%.0f ms")) {
                    limiter->SetReleaseTime(config.audio.limiter_release_time);
                    g_config.AutoSave();
                }
                ImGui::SameLine();
                if (ImGui::Button("Reset##release")) {
                    config.audio.limiter_release_time = 100.0f;
                    limiter->SetReleaseTime(config.audio.limiter_release_time);
                    g_config.AutoSave();
                }

                // Look-ahead Time
                if (ImGui::SliderFloat("Look-ahead (ms)", &config.audio.limiter_lookahead_time, 5.0f, 20.0f, "%.1f ms")) {
                    limiter->SetLookAheadTime(config.audio.limiter_lookahead_time);
                    g_config.AutoSave();
                }
                ImGui::SameLine();
                if (ImGui::Button("Reset##lookahead")) {
                    config.audio.limiter_lookahead_time = 5.0f;
                    limiter->SetLookAheadTime(config.audio.limiter_lookahead_time);
                    g_config.AutoSave();
                }

                ImGui::Spacing();
                ImGui::Separator();

                // Real-time Monitoring
                ImGui::Text("Real-time Monitoring");
                ImGui::Separator();

                // Static variables for graph history
                static std::vector<float> gain_reduction_history(200, 0.0f);
                static std::vector<float> input_level_history(200, -60.0f);
                static std::vector<float> output_level_history(200, -60.0f);
                static int history_offset = 0;

                // Update history data
                float gain_reduction = limiter->GetGainReduction();
                float peak_level = limiter->GetPeakLevel();
                float output_level = limiter->GetOutputLevel();

                gain_reduction_history[history_offset] = gain_reduction;
                input_level_history[history_offset] = peak_level;
                output_level_history[history_offset] = output_level;
                history_offset = (history_offset + 1) % gain_reduction_history.size();

                // Gain Reduction Meter
                ImGui::Text("Gain Reduction: %.1f dB", gain_reduction);
                float gr_normalized = std::max(0.0f, -gain_reduction / 20.0f); // Normalize to 0-1
                ImGui::PushStyleColor(ImGuiCol_PlotHistogram, ImVec4(1.0f, 0.5f, 0.0f, 1.0f));
                ImGui::ProgressBar(gr_normalized, ImVec2(-1, 0),
                    (std::to_string(static_cast<int>(gain_reduction)) + " dB").c_str());
                ImGui::PopStyleColor();

                // Peak Level Meter
                ImGui::Text("Input Peak: %.1f dB", peak_level);
                float peak_normalized = std::max(0.0f, (peak_level + 60.0f) / 60.0f); // -60dB to 0dB
                ImVec4 peak_color = peak_level > -3.0f ? ImVec4(1.0f, 0.0f, 0.0f, 1.0f) :
                                   peak_level > -12.0f ? ImVec4(1.0f, 1.0f, 0.0f, 1.0f) :
                                   ImVec4(0.0f, 1.0f, 0.0f, 1.0f);
                ImGui::PushStyleColor(ImGuiCol_PlotHistogram, peak_color);
                ImGui::ProgressBar(peak_normalized, ImVec2(-1, 0),
                    (std::to_string(static_cast<int>(peak_level)) + " dB").c_str());
                ImGui::PopStyleColor();

                // Output Level Meter
                ImGui::Text("Output Level: %.1f dB", output_level);
                float output_normalized = std::max(0.0f, (output_level + 60.0f) / 60.0f); // -60dB to 0dB
                ImVec4 output_color = output_level > -3.0f ? ImVec4(1.0f, 0.0f, 0.0f, 1.0f) :
                                     output_level > -12.0f ? ImVec4(1.0f, 1.0f, 0.0f, 1.0f) :
                                     ImVec4(0.0f, 1.0f, 0.0f, 1.0f);
                ImGui::PushStyleColor(ImGuiCol_PlotHistogram, output_color);
                ImGui::ProgressBar(output_normalized, ImVec2(-1, 0),
                    (std::to_string(static_cast<int>(output_level)) + " dB").c_str());
                ImGui::PopStyleColor();

                ImGui::Spacing();
                ImGui::Separator();

                // Time-series Graphs
                ImGui::Text("Time-series Graphs");
                ImGui::Separator();

                // Gain Reduction Graph
                ImGui::Text("Gain Reduction History");
                ImGui::PlotLines("##GainReduction", gain_reduction_history.data(),
                    static_cast<int>(gain_reduction_history.size()), history_offset,
                    "Gain Reduction (dB)", -20.0f, 0.0f, ImVec2(0, 80));

                // Input Level Graph
                ImGui::Text("Input Level History");
                ImGui::PlotLines("##InputLevel", input_level_history.data(),
                    static_cast<int>(input_level_history.size()), history_offset,
                    "Input Level (dB)", -60.0f, 0.0f, ImVec2(0, 80));

                // Output Level Graph
                ImGui::Text("Output Level History");
                ImGui::PlotLines("##OutputLevel", output_level_history.data(),
                    static_cast<int>(output_level_history.size()), history_offset,
                    "Output Level (dB)", -60.0f, 0.0f, ImVec2(0, 80));

                ImGui::Spacing();
                ImGui::Separator();

                // Preset Buttons
                ImGui::Text("Presets");
                ImGui::Separator();

                if (ImGui::Button("Gentle Limiter")) {
                    config.audio.limiter_threshold = -12.0f;
                    config.audio.limiter_release_time = 200.0f;
                    config.audio.limiter_lookahead_time = 10.0f;
                    limiter->SetThreshold(config.audio.limiter_threshold);
                    limiter->SetReleaseTime(config.audio.limiter_release_time);
                    limiter->SetLookAheadTime(config.audio.limiter_lookahead_time);
                    g_config.AutoSave();
                }
                ImGui::SameLine();
                if (ImGui::Button("Hard Limiter")) {
                    config.audio.limiter_threshold = -3.0f;
                    config.audio.limiter_release_time = 50.0f;
                    config.audio.limiter_lookahead_time = 5.0f;
                    limiter->SetThreshold(config.audio.limiter_threshold);
                    limiter->SetReleaseTime(config.audio.limiter_release_time);
                    limiter->SetLookAheadTime(config.audio.limiter_lookahead_time);
                    g_config.AutoSave();
                }
                ImGui::SameLine();
                if (ImGui::Button("Brick Wall")) {
                    config.audio.limiter_threshold = -1.0f;
                    config.audio.limiter_release_time = 10.0f;
                    config.audio.limiter_lookahead_time = 5.0f;
                    limiter->SetThreshold(config.audio.limiter_threshold);
                    limiter->SetReleaseTime(config.audio.limiter_release_time);
                    limiter->SetLookAheadTime(config.audio.limiter_lookahead_time);
                    g_config.AutoSave();
                }

                if (ImGui::Button("Transparent")) {
                    config.audio.limiter_threshold = -6.0f;
                    config.audio.limiter_release_time = 100.0f;
                    config.audio.limiter_lookahead_time = 8.0f;
                    limiter->SetThreshold(config.audio.limiter_threshold);
                    limiter->SetReleaseTime(config.audio.limiter_release_time);
                    limiter->SetLookAheadTime(config.audio.limiter_lookahead_time);
                    g_config.AutoSave();
                }

                if (ImGui::Button("Reset All")) {
                    config.audio.limiter_threshold = -6.0f;
                    config.audio.limiter_release_time = 100.0f;
                    config.audio.limiter_lookahead_time = 5.0f;
                    limiter->Reset();
                    limiter->SetThreshold(config.audio.limiter_threshold);
                    limiter->SetReleaseTime(config.audio.limiter_release_time);
                    limiter->SetLookAheadTime(config.audio.limiter_lookahead_time);
                    g_config.AutoSave();
                }

            } else {
                ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.5f, 1.0f), "Audio engine not initialized");
                ImGui::Text("Audio Limiter requires the audio engine to be running");
            }

            ImGui::End();
        }

        // Cheat Tool Window
        if (config.cheat_tool.show_cheat_tool) {
            bool prev_show_cheat_tool = config.cheat_tool.show_cheat_tool;
            ImGui::Begin("Cheat Tool", &config.cheat_tool.show_cheat_tool);
            if (prev_show_cheat_tool && !config.cheat_tool.show_cheat_tool) {
                g_config.AutoSave(); // Save when window is closed via X button
            }

            ImGui::Text("Multioctave Settings");
            ImGui::Separator();

            // Enable/Disable Multioctave
            if (ImGui::Checkbox("Enable Multioctave", &config.cheat_tool.multioctave_enabled)) {
                piano.SetMultioctaveEnabled(config.cheat_tool.multioctave_enabled);
                g_config.AutoSave();
            }

            if (config.cheat_tool.multioctave_enabled) {
                ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "Status: ACTIVE");
            } else {
                ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.5f, 1.0f), "Status: DISABLED");
            }

            ImGui::Spacing();

            // Multioctave Count
            if (ImGui::SliderInt("Octave Count", &config.cheat_tool.multioctave_count, 1, 64, "%d octaves")) {
                piano.SetMultioctaveCount(config.cheat_tool.multioctave_count);
                g_config.AutoSave();
            }
            ImGui::SameLine();
            if (ImGui::Button("Reset##count")) {
                config.cheat_tool.multioctave_count = 1;
                piano.SetMultioctaveCount(config.cheat_tool.multioctave_count);
                g_config.AutoSave();
            }

            // Multioctave Distance
            if (ImGui::SliderInt("Distance (semitones)", &config.cheat_tool.multioctave_distance, 1, 24, "%d semitones")) {
                piano.SetMultioctaveDistance(config.cheat_tool.multioctave_distance);
                g_config.AutoSave();
            }
            ImGui::SameLine();
            if (ImGui::Button("Reset##distance")) {
                config.cheat_tool.multioctave_distance = 12;
                piano.SetMultioctaveDistance(config.cheat_tool.multioctave_distance);
                g_config.AutoSave();
            }

            ImGui::Spacing();
            ImGui::Separator();

            // PC Keyboard Transpose Settings
            ImGui::Text("PC Keyboard Transpose");
            ImGui::Separator();

            // Transpose slider
            if (ImGui::SliderInt("Transpose (semitones)", &config.cheat_tool.pc_keyboard_transpose, -24, 24, "%+d semitones")) {
                g_config.AutoSave();
            }
            ImGui::SameLine();
            if (ImGui::Button("Reset##transpose")) {
                config.cheat_tool.pc_keyboard_transpose = 0;
                g_config.AutoSave();
            }

            // Octave shift slider
            if (ImGui::SliderInt("Octave Shift", &config.cheat_tool.pc_keyboard_octave, -4, 4, "%+d octaves")) {
                g_config.AutoSave();
            }
            ImGui::SameLine();
            if (ImGui::Button("Reset##octave")) {
                config.cheat_tool.pc_keyboard_octave = 0;
                g_config.AutoSave();
            }

            // Show current total offset
            int total_offset = config.cheat_tool.pc_keyboard_transpose + (config.cheat_tool.pc_keyboard_octave * 12);
            if (total_offset != 0) {
                ImGui::Text("Total Offset: %+d semitones", total_offset);
                if (total_offset > 0) {
                    ImGui::SameLine();
                    ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "(Higher)");
                } else {
                    ImGui::SameLine();
                    ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.0f, 1.0f), "(Lower)");
                }
            } else {
                ImGui::Text("Total Offset: No change");
            }

            ImGui::Spacing();
            ImGui::Separator();

            // Help text
            ImGui::Text("Help:");
            ImGui::TextWrapped("Multioctave plays additional notes above and below the original note when you press a key.");
            ImGui::TextWrapped("- Octave Count: How many additional octaves to play (1 = one above and one below)");
            ImGui::TextWrapped("- Distance: Interval in semitones (12 = one octave, 7 = perfect fifth)");

            ImGui::Spacing();
            ImGui::TextWrapped("PC Keyboard Transpose shifts all PC keyboard input by the specified amount:");
            ImGui::TextWrapped("- Transpose: Fine adjustment in semitones (-24 to +24)");
            ImGui::TextWrapped("- Octave Shift: Coarse adjustment in octaves (-4 to +4)");
            ImGui::TextWrapped("- Both settings combine for the total offset");

            ImGui::End();
        }

        // SoundFont Manager Window
        if (config.display.show_soundfont_manager) {
            bool prev_show_soundfont_manager = config.display.show_soundfont_manager;
            ImGui::Begin("SoundFont Manager", &config.display.show_soundfont_manager);
            if (prev_show_soundfont_manager && !config.display.show_soundfont_manager) {
                g_config.AutoSave(); // Save when window is closed via X button
            }

            auto& soundfont_manager = g_audio.GetSoundFontManager();

            // Control buttons
            if (ImGui::Button("Add SoundFont...")) {
                g_soundfont_browser.Open();
            }

            ImGui::SameLine();
            if (ImGui::Button("Load All SoundFonts")) {
                soundfont_manager.LoadAllSoundFonts();
                g_audio.ApplySoundFonts();
            }

            ImGui::SameLine();
            if (ImGui::Button("Apply SoundFonts")) {
                g_audio.ApplySoundFonts();
            }

            ImGui::SameLine();
            if (ImGui::Button("Clear All")) {
                soundfont_manager.ClearAllSoundFonts();
                g_audio.ApplySoundFonts();
                g_config.SaveSoundFontsFromManager(soundfont_manager);
            }

            ImGui::Spacing();
            ImGui::Separator();
            ImGui::Spacing();

            // SoundFont list
            const auto& soundfonts = soundfont_manager.GetSoundFonts();
            if (!soundfonts.empty()) {
                ImGui::Text("SoundFonts (%zu):", soundfonts.size());
                ImGui::Spacing();

                // Table
                if (ImGui::BeginTable("SoundFontTable", 6, ImGuiTableFlags_Borders | ImGuiTableFlags_RowBg | ImGuiTableFlags_Resizable)) {
                    ImGui::TableSetupColumn("Enabled", ImGuiTableColumnFlags_WidthFixed, 60.0f);
                    ImGui::TableSetupColumn("Name", ImGuiTableColumnFlags_WidthStretch);
                    ImGui::TableSetupColumn("Status", ImGuiTableColumnFlags_WidthFixed, 80.0f);
                    ImGui::TableSetupColumn("Volume", ImGuiTableColumnFlags_WidthFixed, 120.0f);
                    ImGui::TableSetupColumn("Priority", ImGuiTableColumnFlags_WidthFixed, 60.0f);
                    ImGui::TableSetupColumn("Actions", ImGuiTableColumnFlags_WidthFixed, 150.0f);
                    ImGui::TableHeadersRow();

                    for (size_t i = 0; i < soundfonts.size(); ++i) {
                        const auto& sf = soundfonts[i];
                        ImGui::TableNextRow();

                        // Enabled checkbox
                        ImGui::TableNextColumn();
                        bool enabled = sf.enabled;
                        if (ImGui::Checkbox(("##enabled" + std::to_string(i)).c_str(), &enabled)) {
                            soundfont_manager.SetSoundFontEnabled(i, enabled);
                            g_audio.ApplySoundFonts();
                            g_config.SaveSoundFontsFromManager(soundfont_manager);
                        }

                        // Name
                        ImGui::TableNextColumn();
                        ImGui::Text("%s", sf.name.c_str());
                        if (ImGui::IsItemHovered()) {
                            ImGui::SetTooltip("%s", sf.path.c_str());
                        }

                        // Status
                        ImGui::TableNextColumn();
                        if (sf.loaded) {
                            ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "Loaded");
                        } else if (sf.should_load) {
                            ImGui::TextColored(ImVec4(1.0f, 1.0f, 0.0f, 1.0f), "Should Load");
                        } else {
                            ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "Unloaded");
                        }

                        // Volume
                        ImGui::TableNextColumn();
                        float volume_percent = sf.volume * 100.0f; // Convert to percentage for display
                        ImGui::PushItemWidth(-1);
                        if (ImGui::SliderFloat(("##volume" + std::to_string(i)).c_str(), &volume_percent, 0.0f, 100.0f, "%.0f%%", ImGuiSliderFlags_AlwaysClamp)) {
                            float volume = volume_percent / 100.0f; // Convert back to 0.0-1.0
                            soundfont_manager.SetSoundFontVolume(i, volume);
                            g_audio.ApplySoundFonts(); // Apply volume change immediately
                            g_config.SaveSoundFontsFromManager(soundfont_manager);
                        }
                        ImGui::PopItemWidth();

                        // Priority
                        ImGui::TableNextColumn();
                        ImGui::Text("%d", sf.priority);

                        // Actions
                        ImGui::TableNextColumn();

                        // Load/Unload button with should_load state indication
                        if (sf.should_load) {
                            if (sf.loaded) {
                                if (ImGui::SmallButton(("Unload##" + std::to_string(i)).c_str())) {
                                    soundfont_manager.SetSoundFontShouldLoad(i, false);
                                    g_config.SaveSoundFontsFromManager(soundfont_manager);
                                }
                            } else {
                                if (ImGui::SmallButton(("Load##" + std::to_string(i)).c_str())) {
                                    soundfont_manager.LoadSoundFont(i);
                                    g_audio.ApplySoundFonts();
                                }
                            }
                        } else {
                            // Show "Load" button when should_load is false
                            if (ImGui::SmallButton(("Load##" + std::to_string(i)).c_str())) {
                                soundfont_manager.SetSoundFontShouldLoad(i, true);
                                g_config.SaveSoundFontsFromManager(soundfont_manager);
                            }
                        }
                        ImGui::SameLine();

                        // Move up button
                        if (i > 0) {
                            if (ImGui::SmallButton(("Up##" + std::to_string(i)).c_str())) {
                                soundfont_manager.MoveSoundFontUp(i);
                                g_audio.ApplySoundFonts();
                                g_config.SaveSoundFontsFromManager(soundfont_manager);
                            }
                            ImGui::SameLine();
                        }

                        // Move down button
                        if (i < soundfonts.size() - 1) {
                            if (ImGui::SmallButton(("Down##" + std::to_string(i)).c_str())) {
                                soundfont_manager.MoveSoundFontDown(i);
                                g_audio.ApplySoundFonts();
                                g_config.SaveSoundFontsFromManager(soundfont_manager);
                            }
                            ImGui::SameLine();
                        }

                        // Remove button
                        if (ImGui::SmallButton(("Remove##" + std::to_string(i)).c_str())) {
                            soundfont_manager.RemoveSoundFont(i);
                            g_audio.ApplySoundFonts();
                            g_config.SaveSoundFontsFromManager(soundfont_manager);
                        }
                    }

                    ImGui::EndTable();
                }
            } else {
                ImGui::Text("No SoundFonts added.");
                ImGui::TextWrapped("Click 'Add SoundFont...' to get started.");
            }

            ImGui::Spacing();
            ImGui::Separator();
            ImGui::TextWrapped("Note: Place .sf2 or .sfz files in accessible directories. Popular soundfonts include FluidR3_GM.sf2, GeneralUser_GS.sf2, or Timbres_Of_Heaven.sf2");

            ImGui::End();
        }

        // Display file browser and handle file selection
        g_soundfont_browser.Display();
        if (g_soundfont_browser.HasSelected()) {
            auto selected_path = g_soundfont_browser.GetSelected();
            std::string path_str = selected_path.string();

            // Add to SoundFont Manager
            if (g_audio.GetSoundFontManager().AddSoundFont(path_str)) {
                std::cout << "Added soundfont to manager: " << path_str << std::endl;

                // Load the newly added soundfont
                g_audio.GetSoundFontManager().LoadSoundFont(path_str);
                g_audio.ApplySoundFonts();

                g_config.SaveSoundFontsFromManager(g_audio.GetSoundFontManager());
            } else {
                std::cerr << "Failed to add soundfont to manager: " << path_str << std::endl;
            }

            g_soundfont_browser.ClearSelected();
        }

        // Rendering
        ImGui::Render();

        // Clear screen with background based on mode
        switch (config.display.background_mode) {
            case 0: // Solid color
                {
                    Color bg_color(config.display.background_color[0], config.display.background_color[1], config.display.background_color[2], 1.0f);
                    renderer.Clear(bg_color);
                }
                break;
            case 1: // Radial gradient
                {
                    Color center_color(config.display.gradient_center_color[0], config.display.gradient_center_color[1], config.display.gradient_center_color[2], 1.0f);
                    Color edge_color(config.display.gradient_edge_color[0], config.display.gradient_edge_color[1], config.display.gradient_edge_color[2], 1.0f);
                    renderer.ClearWithRadialGradient(center_color, edge_color);
                }
                break;
            case 2: // Transparent background
                {
                    renderer.ClearTransparent(config.display.transparency_alpha);
                }
                break;
            case 3: // Image background
                {
                    renderer.ClearWithImage(config.display.background_image_path, config.display.background_image_opacity, config.display.background_image_scale_mode);
                }
                break;
            default: // Fallback to solid color
                {
                    Color bg_color(config.display.background_color[0], config.display.background_color[1], config.display.background_color[2], 1.0f);
                    renderer.Clear(bg_color);
                }
                break;
        }

        // Update piano keyboard blips (remove expired ones)
        piano.UpdateBlips();

        // Render piano keyboard with OpenGL
        piano.Render(renderer);

        // Render note indicator overlay
        g_note_indicator.Render(renderer);

        // Render ImGui on top
        ImGui_ImplOpenGL3_RenderDrawData(ImGui::GetDrawData());

        glfwSwapBuffers(window);
    }

    // Cleanup
    ImGui_ImplOpenGL3_Shutdown();
    ImGui_ImplGlfw_Shutdown();
    ImGui::DestroyContext();

    glfwDestroyWindow(window);
    glfwTerminate();

    return 0;
}
